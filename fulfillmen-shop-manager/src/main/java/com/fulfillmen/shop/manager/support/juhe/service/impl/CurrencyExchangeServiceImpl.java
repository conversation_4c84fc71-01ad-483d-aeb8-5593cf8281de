/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.service.impl;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriBuilder;
import org.springframework.web.util.UriComponentsBuilder;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.feiniaojin.gracefulresponse.GracefulResponseException;

import com.fulfillmen.shop.domain.res.juhe.CurrencyRateInfoRes;
import com.fulfillmen.shop.domain.res.juhe.CurrencyRateInfoRes.ResultDTO;
import com.fulfillmen.shop.manager.support.configure.Currency;
import com.fulfillmen.shop.manager.support.configure.JuheApiPaths;
import com.fulfillmen.shop.manager.support.service.ICurrencyExchangeService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 货币汇率转换服务实现
 *
 * <p>整合汇率获取和货币转换功能，提供统一的货币转换服务</p>
 *
 * <AUTHOR>
 * @date 2025/6/5
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CurrencyExchangeServiceImpl implements ICurrencyExchangeService, InitializingBean {

    private final WebClient fmWebClient;
    private final CacheManager cacheManager;

    /**
     * 货币汇率缓存
     */
    private Cache<String, List<ResultDTO>> currencyRateCache;

    @Override
    public BigDecimal exchangeCurrency(BigDecimal amount, String fromCurrency, String toCurrency) {
        // 参数验证
        validateCurrencyExchangeParams(amount, fromCurrency, toCurrency);

        // 相同货币直接返回
        if (fromCurrency.equals(toCurrency)) {
            return amount;
        }

        // 获取汇率并转换
        BigDecimal exchangeRate = getExchangeRate(fromCurrency, toCurrency);
        BigDecimal result = convertAmount(amount, exchangeRate);

        log.info("货币转换: {} {} → {} {} (汇率: {})", amount, fromCurrency, result, toCurrency, exchangeRate);

        return result;
    }

    @Override
    public BigDecimal getExchangeRate(String fromCurrency, String toCurrency) {
        // 参数验证
        validateCurrencyParams(fromCurrency, toCurrency);

        // 相同货币汇率为1
        if (fromCurrency.equals(toCurrency)) {
            return BigDecimal.ONE;
        }

        // 获取汇率数据
        List<ResultDTO> rateList = getRealTimeCurrencyExchange(fromCurrency, toCurrency);

        // 查找匹配的汇率记录
        ResultDTO targetRate = findMatchingRate(rateList, fromCurrency, toCurrency);
        if (targetRate == null) {
            throw new GracefulResponseException("无法找到" + fromCurrency + "到" + toCurrency + "的汇率");
        }

        return new BigDecimal(targetRate.getExchange());
    }

    @Override
    public String formatExchangeResult(BigDecimal originalAmount,
        String fromCurrency,
        BigDecimal convertedAmount,
        String toCurrency) {
        return formatCurrency(originalAmount, fromCurrency) + " = " + formatCurrency(convertedAmount, toCurrency);
    }

    /**
     * 获取实时汇率数据（带手动缓存）
     */
    private List<ResultDTO> getRealTimeCurrencyExchange(String from, String to) {
        // 构建缓存key
        String cacheKey = from + ":" + to;

        // 先从缓存获取
        List<ResultDTO> cachedResult = currencyRateCache.get(cacheKey);
        if (cachedResult != null) {
            log.debug("从缓存获取汇率数据: {} -> {}", from, to);
            return cachedResult;
        }

        // 缓存未命中，调用API
        log.info("调用汇率API获取{}到{}的汇率", from, to);
        List<ResultDTO> apiResult = callCurrencyApi(from, to);

        // 将结果存入缓存
        currencyRateCache.put(cacheKey, apiResult);
        log.debug("汇率数据已缓存: {} -> {}", from, to);

        return apiResult;
    }

    /**
     * 调用货币汇率API
     */
    private List<ResultDTO> callCurrencyApi(String from, String to) {
        UriBuilder uriBuilder = buildApiUri();
        uriBuilder.queryParam("from", from);
        uriBuilder.queryParam("to", to);

        Mono<CurrencyRateInfoRes> responseMono = fmWebClient.get()
            .uri(uriBuilder.toUriString())
            .accept(MediaType.APPLICATION_JSON)
            .retrieve()
            .bodyToMono(CurrencyRateInfoRes.class)
            .doOnSuccess(response -> log.debug("汇率API调用成功: {}", response.getReason()))
            .doOnError(error -> log.error("汇率API调用失败", error));

        return responseMono.<List<ResultDTO>>handle((response, sink) -> {
            if (!isValidResponse(response)) {
                sink.error(new GracefulResponseException("汇率API返回数据无效"));
                return;
            }
            sink.next(response.getResult());
        }).block();
    }

    /**
     * 构建API请求URI
     */
    private UriBuilder buildApiUri() {
        UriBuilder uriBuilder = UriComponentsBuilder.fromUriString(JuheApiPaths.REAL_TIME_CURRENCY_EXCHANGE_URL);
        uriBuilder.queryParam("key", JuheApiPaths.API_KEY);
        uriBuilder.queryParam("version", "2");
        return uriBuilder;
    }

    /**
     * 根据货币对查找匹配的汇率记录
     */
    private ResultDTO findMatchingRate(List<ResultDTO> rateList, String fromCurrency, String toCurrency) {
        return rateList.stream()
            .filter(rate -> fromCurrency.equals(rate.getCurrencyFrom()) && toCurrency.equals(rate.getCurrencyTo()))
            .findFirst()
            .orElse(null);
    }

    /**
     * 验证API响应
     */
    private boolean isValidResponse(CurrencyRateInfoRes response) {
        return response != null && response.getResult() != null && response.getErrorCode() == 0;
    }

    /**
     * 验证货币转换参数
     */
    private void validateCurrencyExchangeParams(BigDecimal amount, String fromCurrency, String toCurrency) {
        if (amount == null) {
            throw new IllegalArgumentException("金额不能为空");
        }
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("金额不能为负数");
        }
        validateCurrencyParams(fromCurrency, toCurrency);
    }

    /**
     * 验证货币参数
     */
    private void validateCurrencyParams(String fromCurrency, String toCurrency) {
        if (fromCurrency == null || fromCurrency.trim().isEmpty()) {
            throw new IllegalArgumentException("源货币代码不能为空");
        }
        if (toCurrency == null || toCurrency.trim().isEmpty()) {
            throw new IllegalArgumentException("目标货币代码不能为空");
        }
        // 使用Currency工具类进行验证
        if (!Currency.isValidCurrency(fromCurrency)) {
            throw new IllegalArgumentException("无效的源货币代码: " + fromCurrency + ", 支持的货币: " + Currency
                .getCurrencyNameByCode(fromCurrency));
        }
        if (!Currency.isValidCurrency(toCurrency)) {
            throw new IllegalArgumentException("无效的目标货币代码: " + toCurrency + ", 支持的货币: " + Currency
                .getCurrencyNameByCode(toCurrency));
        }
    }

    /**
     * 货币转换计算
     */
    private BigDecimal convertAmount(BigDecimal amount, BigDecimal exchangeRate) {
        if (amount == null || exchangeRate == null) {
            throw new IllegalArgumentException("金额和汇率不能为空");
        }
        return amount.multiply(exchangeRate);
    }

    /**
     * 格式化货币显示
     */
    private String formatCurrency(BigDecimal amount, String currencyCode) {
        if (amount == null || currencyCode == null) {
            return "N/A";
        }
        String currencyName = Currency.getCurrencyNameByCode(currencyCode);
        return amount.toString() + " " + currencyName + "(" + currencyCode + ")";
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化货币汇率缓存
        QuickConfig quickConfig = QuickConfig.newBuilder("currency.rate.")
            .localExpire(Duration.ofHours(12))  // 本地缓存12小时
            .expire(Duration.ofHours(24))       // 远程缓存24小时
            .cacheType(CacheType.BOTH)          // 二级缓存
            .syncLocal(true)                    // 同步本地缓存
            .build();
        currencyRateCache = cacheManager.getOrCreateCache(quickConfig);
        log.info("货币汇率缓存初始化完成");
    }
}
