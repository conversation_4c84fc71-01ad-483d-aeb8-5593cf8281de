/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.req.AggregateSearchReq;
import com.fulfillmen.shop.frontend.convert.FrontendProductConvert;
import com.fulfillmen.shop.frontend.service.IProductService;
import com.fulfillmen.shop.frontend.vo.ProductDetailVO;
import com.fulfillmen.shop.frontend.vo.ProductInfoVO;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.service.IProductSyncService;
import com.fulfillmen.shop.manager.support.alibaba.IProductManager;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品搜索服务实现
 *
 * <AUTHOR>
 * @date 2025/2/25 19:15
 * @since 1.0.0
 */
@Slf4j
@Service
public class ProductServiceImpl implements IProductService, InitializingBean {

    private final IProductManager productManager;
    private final IProductSyncService productSyncService;
    private final CacheManager cacheManager;
    private final PdcProductMappingRepository pdcProductMappingRepository;
    private final TzProductSpuMapper productSpuMapper;

    /**
     * Redis缓存 - ProductDetailVO (15分钟)
     */
    private Cache<String, ProductDetailVO> productDetailVOCache;

    public ProductServiceImpl(IProductManager productManager,
        IProductSyncService productSyncService,
        CacheManager cacheManager,
        PdcProductMappingRepository pdcProductMappingRepository, TzProductSpuMapper productSpuMapper) {
        this.productManager = productManager;
        this.productSyncService = productSyncService;
        this.cacheManager = cacheManager;
        this.pdcProductMappingRepository = pdcProductMappingRepository;
        this.productSpuMapper = productSpuMapper;
    }

    /**
     * 上传图片获取图片ID
     *
     * <pre>
     * 1. 文件类型校验 (仅允许图片格式)
     * 2. 文件大小校验 (限制2MB)
     * 3. 图片转Base64编码
     * 4. 调用底层服务上传图片
     * 5. 异常处理逻辑
     * </pre>
     *
     * @param file 图片文件
     * @return 图片ID，用于后续图片搜索
     */
    @Override
    public String uploadImage(MultipartFile file) {
        log.debug("uploadImage request: filename={}, size={}, contentType={}", file.getOriginalFilename(), file
            .getSize(), file.getContentType());
        return productManager.uploadImage(file);
    }

    @Override
    public PageDTO<ProductInfoDTO> searchSimilarProductsSyncDb(Long offerId, Integer pageIndex, Integer pageSize) {
        return pdcProductMappingRepository.searchSimilarProductsSync(offerId, LanguageEnum.EN, pageIndex, pageSize);
    }

    @Override
    public PageDTO<ProductInfoVO> unifiedAggregateSearch(AggregateSearchReq request, Boolean useCache) {
        log.info("聚合搜索VO: searchType={}, keyword={}", request.getSearchType(), request.getKeyword());

        try {
            // 1. 使用 PdcProductMappingRepository 获取聚合搜索结果
            PageDTO<ProductInfoDTO> searchResult = pdcProductMappingRepository
                .unifiedAggregateSearchWithCache(request, !useCache);

            if (searchResult == null || CollectionUtils.isEmpty(searchResult.getRecords())) {
                log.debug("聚合搜索结果为空: searchType={}, keyword={}", request.getSearchType(), request.getKeyword());
                return PageDTO.<ProductInfoVO>builder()
                    .records(List.of())
                    .pageIndex(request.getPage() != null ? request.getPage() : 1)
                    .pageSize(request.getPageSize() != null ? request.getPageSize() : 10)
                    .total(0L)
                    .build();
            }

            // 3. 转换为前端VO
            PageDTO<ProductInfoVO> voPage = FrontendProductConvert.INSTANCE.toProductInfoVOPage(searchResult);

            log.debug("聚合搜索VO完成，结果数量: {}", voPage.getRecords().size());
            return voPage;

        } catch (Exception e) {
            log.error("聚合搜索VO异常: searchType={}, keyword={}", request.getSearchType(), request.getKeyword(), e);
            return PageDTO.<ProductInfoVO>builder()
                .records(List.of())
                .pageIndex(request.getPage() != null ? request.getPage() : 1)
                .pageSize(request.getPageSize() != null ? request.getPageSize() : 10)
                .total(0L)
                .build();
        }
    }

    /**
     * 🎯 Task 1.4: 清理产品详情缓存
     *
     * @param platformProductId 平台产品ID，null表示清理所有
     * @return 清理结果描述
     */
    @Override
    public String clearProductDetailCache(String platformProductId) {
        try {
            if (platformProductId != null) {
                // 清理指定产品的缓存
                String cacheKey = "productDetail:" + platformProductId;

                productDetailVOCache.remove(cacheKey);

                log.info("已清理产品详情缓存: platformProductId={}", platformProductId);
                return "已清理产品 " + platformProductId + " 的缓存";
            } else {
                // 清理所有缓存 - 注意：这个操作比较重，建议谨慎使用
                log.warn("请求清理所有产品详情缓存");
                return "清理所有缓存功能暂未实现，请指定具体的产品ID";
            }
        } catch (Exception e) {
            log.error("清理产品详情缓存失败: platformProductId={}", platformProductId, e);
            return "清理缓存失败: " + e.getMessage();
        }
    }

    /**
     * 🎯 Task 1.4: 预热产品详情缓存
     *
     * @param platformProductIds 需要预热的产品ID列表
     * @return 预热结果描述
     */
    public String warmupProductDetailCache(List<String> platformProductIds) {
        if (CollectionUtil.isEmpty(platformProductIds)) {
            return "预热列表为空";
        }

        log.info("开始预热产品详情缓存，数量: {}", platformProductIds.size());

        int successCount = 0;
        int failCount = 0;

        for (String platformProductId : platformProductIds) {
            try {
                // 强制刷新缓存
                ProductDetailVO vo = getProductDetailVOWithCache(platformProductId, true);
                if (vo != null) {
                    successCount++;
                    log.debug("预热成功: platformProductId={}", platformProductId);
                } else {
                    failCount++;
                    log.warn("预热失败，产品不存在: platformProductId={}", platformProductId);
                }

                // 避免请求过于频繁
                Thread.sleep(50);

            } catch (Exception e) {
                failCount++;
                log.error("预热异常: platformProductId={}", platformProductId, e);
            }
        }

        String result = String.format("预热完成，成功: %d，失败: %d", successCount, failCount);
        log.info(result);
        return result;
    }

    // ==================== 前端VO方法实现 ====================

    @Override
    public ProductDetailVO getProductDetailVO(Long id) {
        log.debug("获取商品详情VO: 传入ID={}", id);
        String platformProductId;

        // 1. 优先将 id 视为 TzProductSpu.id 进行查询
        LambdaQueryWrapper<TzProductSpu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(TzProductSpu::getId, id)
            .or()
            .eq(TzProductSpu::getPdcPlatformProductId, id);
        TzProductSpu productSpu = productSpuMapper.selectOne(queryWrapper);

        if (Objects.nonNull(productSpu)) {
            // 1.1 如果找到了Spu记录, 使用其关联的 platformProductId
            platformProductId = productSpu.getPdcPlatformProductId();
            log.debug("ID被识别为spuId: {}，使用其关联的platformProductId: {}", id, platformProductId);

            if (platformProductId == null) {
                log.error("❌ SPU数据不完整，缺少platformProductId: spuId={}", id);
                throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PRODUCT_DATA_INCOMPLETE, "SPU: " + id);
            }
        } else {
            // 1.2 如果未找到Spu记录, 则将传入的 id 视为 platformProductId
            platformProductId = String.valueOf(id);
            log.debug("ID未找到对应SPU记录，将其视为platformProductId: {}", platformProductId);
        }

        // 2. 使用解析出的 platformProductId 调用带缓存的获取方法
        return getProductDetailVOWithCache(platformProductId, false);
    }

    @Override
    public ProductDetailVO reSyncProductDetailVO(Long id) {
        TzProductDTO productDTO = this.productSyncService.resyncProductByPlatformId(String.valueOf(id), true);
        return FrontendProductConvert.INSTANCE.toProductDetailVOFromTzProductDTO(productDTO);
    }

    /**
     * 带多级缓存的产品详情获取（重构版）
     *
     * <pre>
     * 职责简化：
     * 1. 缓存管理（ProductDetailVO + TzProductDTO）
     * 2. 数据转换（TzProductDTO -> ProductDetailVO）
     * 3. 通过ProductSyncService统一获取产品数据
     * </pre>
     */
    private ProductDetailVO getProductDetailVOWithCache(String platformProductId, boolean forceRefresh) {
        String cacheKey = "productDetail:" + platformProductId;

        try {
            // 1. 尝试从ProductDetailVO缓存获取
            if (!forceRefresh) {
                ProductDetailVO cachedVO = productDetailVOCache.get(cacheKey);
                if (cachedVO != null) {
                    log.debug("命中ProductDetailVO缓存: platformProductId={}", platformProductId);
                    return cachedVO;
                }
            }

            // 2. 缓存未命中，通过简化的ProductSyncService获取数据
            log.debug("缓存未命中，通过简化的ProductSyncService获取: platformProductId={}", platformProductId);

            // 使用简化后的统一入口方法
            TzProductDTO productDTO = productSyncService.getOrSyncProductByPlatformId(platformProductId);

            if (productDTO == null) {
                log.error("❌ 无法获取产品信息: platformProductId={}", platformProductId);
                throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PRODUCT_NOT_FOUND, platformProductId);
            }

            // 3. 验证产品数据完整性
            validateProductDataIntegrity(productDTO, platformProductId);

            // 4. 转换为前端VO
            ProductDetailVO productDetailVO = convertTzProductDTOToVO(productDTO);

            // 5. 更新多级缓存
            updateCaches(cacheKey, productDTO, productDetailVO, platformProductId);

            log.debug("✅ 商品详情VO转换完成: platformProductId={}, spuId={}", platformProductId, productDTO.getId());
            return productDetailVO;

        } catch (BusinessExceptionI18n e) {
            log.warn("❌ 获取商品详情VO失败: platformProductId={}, 错误码={}, 错误信息={}", platformProductId, e.getErrorCode(), e
                .getMessage());
            throw e;
        } catch (Exception e) {
            log.error("❌ 获取商品详情VO异常: platformProductId={}", platformProductId, e);
            // 最终降级逻辑
            return fallbackToOriginalLogic(Long.valueOf(platformProductId));
        }
    }

    /**
     * 更新多级缓存
     */
    private void updateCaches(String cacheKey,
        TzProductDTO productDTO,
        ProductDetailVO productDetailVO,
        String platformProductId) {
        try {
            productDetailVOCache.put(cacheKey, productDetailVO);
            log.debug("✅ 更新多级缓存完成: platformProductId={}", platformProductId);
        } catch (Exception e) {
            log.warn("⚠️ 更新缓存失败: platformProductId={}", platformProductId, e);
        }
    }

    /**
     * 验证产品数据完整性
     */
    private void validateProductDataIntegrity(TzProductDTO productDTO, String platformProductId) {
        if (productDTO.getId() == null) {
            log.warn("⚠️ 产品ID为空，但继续处理: platformProductId={}", platformProductId);
        }

        if (productDTO.getTitle() == null || productDTO.getTitle().trim().isEmpty()) {
            log.warn("⚠️ 产品标题为空，但继续处理: platformProductId={}", platformProductId);
        }

        if (CollectionUtil.isEmpty(productDTO.getSkuList())) {
            log.warn("⚠️ 产品无SKU信息，但继续处理: platformProductId={}", platformProductId);
        } else {
            log.debug("✅ 产品数据验证通过: platformProductId={}, SKU数量={}", platformProductId, productDTO.getSkuList().size());
        }
    }

    /**
     * 转换 TzProductDTO 为 ProductDetailVO
     */
    private ProductDetailVO convertTzProductDTOToVO(TzProductDTO productDTO) {
        return FrontendProductConvert.INSTANCE.toProductDetailVOFromTzProductDTO(productDTO);
    }

    /**
     * 降级逻辑：直接从阿里巴巴产品详情获取数据
     */
    private ProductDetailVO fallbackToOriginalLogic(Long offerId) {
        try {
            log.warn("🔄 降级到阿里巴巴产品详情: offerId={}", offerId);

            // 通过ProductSyncService获取阿里巴巴产品详情
            AlibabaProductDetailDTO productDetail = productSyncService.getAlibabaProductDetail(String
                .valueOf(offerId), false);

            if (productDetail == null) {
                log.error("❌ 降级方案也无法找到产品: offerId={}", offerId);
                throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PRODUCT_NOT_FOUND, String.valueOf(offerId));
            }

            // 使用转换器进行数据转换
            ProductDetailVO fallbackVO = FrontendProductConvert.INSTANCE.toProductDetailVO(productDetail);

            if (fallbackVO == null) {
                log.error("❌ 数据转换失败: offerId={}", offerId);
                throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PRODUCT_DATA_INCOMPLETE, String
                    .valueOf(offerId));
            }

            log.warn("⚠️ 降级逻辑成功，返回基础产品信息: offerId={}, title={}", offerId, fallbackVO.getTitle());
            return fallbackVO;

        } catch (BusinessExceptionI18n e) {
            throw e;
        } catch (Exception e) {
            log.error("❌ 降级方案查询异常: offerId={}", offerId, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_SERVICE_DEGRADED, String
                .valueOf(offerId));
        }
    }

    @Override
    public PageDTO<ProductInfoVO> searchSimilarProductsVO(String imagesUrl, Integer pageIndex, Integer pageSize) {
        log.info("搜索相似商品VO: imagesUrl={}, pageIndex={}, pageSize={}", imagesUrl, pageIndex, pageSize);

        // try {
        //     // 1. 通过PdcProductMappingRepository获取相似商品数据
        //     PageDTO<ProductInfoDTO> similarProductsPage = pdcProductMappingRepository
        //         .searchSimilarProductsSync(imagesUrl, LanguageEnum.EN, pageIndex, pageSize);

        //     // 2. 检查结果
        //     if (similarProductsPage == null || CollectionUtils.isEmpty(similarProductsPage.getRecords())) {
        //         log.debug("相似商品搜索结果为空: offerId={}", offerId);
        //         return PageDTO.<ProductInfoVO>builder()
        //             .records(List.of())
        //             .pageIndex(pageIndex != null ? pageIndex : 1)
        //             .pageSize(pageSize != null ? pageSize : 10)
        //             .total(0L)
        //             .build();
        //     }

        //     // 4. 转换为前端VO
        //     PageDTO<ProductInfoVO> voPage = FrontendProductConvert.INSTANCE.toProductInfoVOPage(similarProductsPage);

        //     log.debug("相似商品VO搜索完成，结果数量: {}", voPage.getRecords().size());
        //     return voPage;

        // } catch (Exception e) {
        //     log.error("搜索相似商品VO异常: offerId={}", offerId, e);
        //     return PageDTO.<ProductInfoVO>builder()
        //         .records(List.of())
        //         .pageIndex(pageIndex != null ? pageIndex : 1)
        //         .pageSize(pageSize != null ? pageSize : 10)
        //         .total(0L)
        //         .build();
        // }
        return null;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 🎯 Phase 1.4: 优化多级缓存配置
        initializeOptimizedCacheConfiguration();
        logCacheConfigurationSummary();
    }

    // ==================== 🎯 Phase 1.4: 优化缓存配置方法 ====================

    /**
     * 🎯 Phase 1.4: 初始化优化的缓存配置
     *
     * <pre>
     * 缓存策略优化：
     * 1. L1缓存(ProductDetailVO): 完整展示数据，中时效，减少转换开销
     * 3. 智能失效策略: 本地+远程双重保障
     * 4. 容量控制: 根据业务特点设置合理容量
     * </pre>
     */
    private void initializeOptimizedCacheConfiguration() {
        // 缓存: ProductDetailVO - 前端展示缓存
        productDetailVOCache = createProductDetailVOCache();

        log.debug("缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]");
    }

    /**
     * 创建ProductDetailVO缓存配置
     *
     * <pre>
     * 优化策略：
     * - 本地缓存8分钟: 前端展示数据较少变化
     * - 远程缓存25分钟: 完整VO对象，转换成本高
     * - 容量300: 支持更多商品详情页访问
     * - 同步机制: 确保多实例数据一致性
     * </pre>
     */
    private Cache<String, ProductDetailVO> createProductDetailVOCache() {
        QuickConfig config = QuickConfig.newBuilder("frontend:product:vo:")
            // 优化: 本地缓存时间调整到8分钟，平衡用户体验和数据一致性
            .localExpire(Duration.ofMinutes(8))
            // 优化: 远程缓存时间调整到25分钟，减少重复转换
            .expire(Duration.ofMinutes(25))
            // 开启空值缓存，防止无效请求重复处理
            .cacheNullValue(true)
            // 双层缓存：本地 + 远程
            .cacheType(CacheType.BOTH)
            // 优化: 本地缓存容量增加到300，支持更多商品详情
            .localLimit(300)
            // 本地缓存同步，保证用户看到一致的商品信息
            .syncLocal(true)
            .build();
        return cacheManager.getOrCreateCache(config);
    }

    /**
     * 记录缓存配置摘要信息
     */
    private void logCacheConfigurationSummary() {
        log.debug("🎯 ProductService优化缓存配置完成:");
        log.debug("  ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:");
        log.debug("  ├── 双层缓存策略: 本地内存 + Redis远程");
        log.debug("  ├── 空值缓存: 已启用，防止缓存穿透");
        log.debug("  └── 同步机制: 已启用，保证多实例一致性");
    }

}
